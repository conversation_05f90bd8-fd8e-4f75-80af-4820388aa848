#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include "link_protocol.h"

static const char* TAG = "MAIN";

uint8_t ble_addr_type;

// Test function to demonstrate advertising
void test_advertising()
{
    ESP_LOGI(TAG, "Testing BLE advertising...");

    // Example 128-bit UUID: 12345678-1234-5678-9ABC-DEF012345678
    std::string test_uuid = "123456781234567889ABCDEF01234567";
    uint8_t test_version = 2;
    std::string device_name = "LinkPet";

    // Start advertising using C++ convenience function
    int result = ble_start_advertising_cpp(test_uuid, test_version, device_name);
    if (result == 0) {
        ESP_LOGI(TAG, "Advertising started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start advertising: %d", result);
    }
}

extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service

    // Set a custom sync callback that will start advertising
    ble_hs_cfg.sync_cb = [](void) {
        ble_hs_id_infer_auto(0, &ble_addr_type);
        // Start advertising after sync
        test_advertising();
    };

    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}
