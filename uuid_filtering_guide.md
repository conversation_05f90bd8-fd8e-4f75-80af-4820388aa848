# UUID过滤器使用指南

## 概述

本系统现在强制要求使用UUID过滤器进行BLE扫描，专注于128位UUID设备的发现和过滤。

## 主要变更

### 移除的功能
- ✗ 16位UUID支持
- ✗ 32位UUID支持  
- ✗ 无过滤器的扫描功能
- ✗ `ble_scan_start_and_wait_json()` 无参数版本

### 保留的功能
- ✓ 128位UUID支持
- ✓ 强制UUID过滤
- ✓ 过滤结果的JSON输出

## API函数

### 扫描函数

```cpp
// 唯一的扫描函数 - UUID过滤器是必需的
std::string ble_scan_start_and_wait_json(const std::string& uuid_filter);
```

**参数:**
- `uuid_filter`: 必需的UUID过滤字符串，不能为空

**返回值:**
- 成功时返回包含过滤设备的JSON字符串
- 失败时返回包含错误信息的JSON字符串

## 使用示例

### 基本过滤

```cpp
// 查找包含"ABCD"的UUID
std::string result = ble_scan_start_and_wait_json("ABCD");

// 查找特定的完整UUID
std::string result = ble_scan_start_and_wait_json("123456789ABCDEF0FEDCBA0987654321");

// 查找UUID前缀
std::string result = ble_scan_start_and_wait_json("12345678");
```

### 错误处理

```cpp
// 空过滤器会返回错误
std::string error_result = ble_scan_start_and_wait_json("");
// 返回: {"success":false,"error":"UUID filter is required","count":0,"total_found":0,"devices":[]}

// 解析结果
cJSON *json = cJSON_Parse(error_result.c_str());
if (json) {
    cJSON *success = cJSON_GetObjectItem(json, "success");
    if (cJSON_IsBool(success) && !cJSON_IsTrue(success)) {
        cJSON *error = cJSON_GetObjectItem(json, "error");
        if (cJSON_IsString(error)) {
            ESP_LOGE("SCAN", "Scan error: %s", error->valuestring);
        }
    }
    cJSON_Delete(json);
}
```

## JSON输出格式

### 成功响应

```json
{
  "success": true,
  "count": 2,
  "total_found": 5,
  "uuid_filter": "ABCD",
  "devices": [
    {
      "name": "Device1",
      "addr": "AA:BB:CC:DD:EE:FF",
      "rssi": -45,
      "uuids128": [
        "ABCDEF0123456789FEDCBA9876543210",
        "123456789ABCDEF0FEDCBA0987654321"
      ]
    },
    {
      "name": "Device2", 
      "addr": "11:22:33:44:55:66",
      "rssi": -67,
      "uuids128": [
        "ABCD1234567890ABCDEF1234567890AB"
      ]
    }
  ]
}
```

### 错误响应

```json
{
  "success": false,
  "error": "UUID filter is required",
  "count": 0,
  "total_found": 0,
  "devices": []
}
```

## 字段说明

- `success`: 布尔值，表示扫描是否成功
- `count`: 通过过滤器的设备数量
- `total_found`: 扫描到的总设备数量
- `uuid_filter`: 使用的UUID过滤字符串
- `devices`: 设备数组
  - `name`: 设备名称
  - `addr`: 设备MAC地址
  - `rssi`: 信号强度
  - `uuids128`: 128位UUID数组（十六进制字符串）

## 过滤逻辑

过滤器使用字符串包含匹配：

```cpp
// 伪代码
for (const auto &uuid : device.uuids128) {
    if (uuid.find(uuid_filter) != std::string::npos) {
        include_device = true;
        break;
    }
}
```

### 过滤示例

假设设备有UUID: `"123456789ABCDEF0FEDCBA9876543210"`

- ✓ `"1234"` - 匹配（前缀）
- ✓ `"ABCD"` - 匹配（中间）
- ✓ `"3210"` - 匹配（后缀）
- ✓ `"123456789ABCDEF0FEDCBA9876543210"` - 匹配（完整）
- ✗ `"WXYZ"` - 不匹配
- ✗ `""` - 错误（空过滤器）

## 最佳实践

### 1. 选择合适的过滤器长度
```cpp
// 太短 - 可能匹配太多设备
std::string result = ble_scan_start_and_wait_json("12");

// 合适 - 4-8个字符通常是好的平衡
std::string result = ble_scan_start_and_wait_json("1234ABCD");

// 太长 - 可能匹配太少设备
std::string result = ble_scan_start_and_wait_json("123456789ABCDEF0FEDCBA9876543210EXTRA");
```

### 2. 错误处理
```cpp
std::string result = ble_scan_start_and_wait_json("ABCD");
cJSON *json = cJSON_Parse(result.c_str());
if (json) {
    cJSON *success = cJSON_GetObjectItem(json, "success");
    if (cJSON_IsBool(success) && cJSON_IsTrue(success)) {
        // 处理成功结果
        cJSON *devices = cJSON_GetObjectItem(json, "devices");
        // ...
    } else {
        // 处理错误
        ESP_LOGE("SCAN", "Scan failed");
    }
    cJSON_Delete(json);
}
```

### 3. 性能考虑
- 使用更具体的过滤器减少不必要的设备处理
- 避免过于频繁的扫描调用
- 考虑使用Link循环进行周期性扫描

## 与Link循环集成

Link循环会自动进行过滤扫描，但不会与手动扫描冲突：

```cpp
// 启动Link循环
start_link_cycle_cpp("ABCDEF01", 1, "MyDevice");

// 在Link循环运行时进行独立扫描（不推荐）
// std::string result = ble_scan_start_and_wait_json("1234");

// 推荐：停止Link循环后再扫描
stop_link_cycle();
std::string result = ble_scan_start_and_wait_json("1234");
```
