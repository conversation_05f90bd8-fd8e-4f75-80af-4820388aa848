# Link循环功能使用示例

## 功能概述

Link循环功能实现了一个400ms周期的BLE操作循环，包含三个阶段：

1. **扫描阶段** (0-150ms): 扫描附近的BLE设备
2. **随机Back-off阶段** (150-170ms): 随机等待0-20ms避免冲突
3. **广播阶段** (170-400ms): 广播自己的信息

这种循环模式适用于需要定期发现和广播的BLE网络协议。

## API函数

### C接口

```c
// 启动link循环（使用默认配置）
int start_link_cycle(void);

// 启动link循环（自定义配置）
int start_link_cycle_with_config(const char* uuid128_hex, uint8_t version, const char* device_name);

// 停止link循环
int stop_link_cycle(void);

// 短时间扫描函数
void ble_app_scan_short(int duration_ms);
```

### C++接口

```cpp
// 启动link循环（C++便利函数）
int start_link_cycle_cpp(const std::string& uuid128_hex, uint8_t version, const std::string& device_name);
```

## 使用示例

### 示例1：使用默认配置启动link循环

```c
// 启动link循环（使用默认UUID和设备名）
int result = start_link_cycle();
if (result == 0) {
    printf("Link循环启动成功\n");
} else {
    printf("Link循环启动失败: %d\n", result);
}

// 运行一段时间后停止
vTaskDelay(pdMS_TO_TICKS(10000)); // 运行10秒
stop_link_cycle();
```

### 示例2：使用自定义配置

```c
// 自定义128位UUID (32个十六进制字符)
const char* custom_uuid = "ABCDEF0123456789FEDCBA9876543210";
uint8_t version = 5;
const char* device_name = "MyLinkDevice";

// 启动link循环
int result = start_link_cycle_with_config(custom_uuid, version, device_name);
if (result == 0) {
    printf("自定义Link循环启动成功\n");
} else {
    printf("Link循环启动失败: %d\n", result);
}
```

### 示例3：使用C++接口

```cpp
// 使用C++字符串
std::string uuid = "123456789ABCDEF0FEDCBA0987654321";
uint8_t version = 2;
std::string device_name = "LinkProtocol-Device";

// 启动link循环
int result = start_link_cycle_cpp(uuid, version, device_name);
if (result == 0) {
    ESP_LOGI("APP", "Link循环启动成功");
} else {
    ESP_LOGE("APP", "Link循环启动失败: %d", result);
}
```

## 时序说明

每个400ms周期包含以下阶段：

```
0ms     150ms   170ms                400ms
|-------|-------|-------------------|
扫描阶段  Back-off    广播阶段
150ms   0-20ms      230ms
```

### 扫描阶段 (0-150ms)
- 停止当前广播（如果有）
- 清空之前的扫描结果
- 开始150ms的BLE扫描
- 收集附近设备的信息

### 随机Back-off阶段 (150-170ms)
- 随机等待0-20ms
- 避免多个设备同时开始广播造成冲突
- 使用ESP32硬件随机数生成器

### 广播阶段 (170-400ms)
- 开始BLE广播，包含：
  - 自定义128位UUID
  - 版本信息
  - 设备名称
  - LINK_PROTOCOL制造商数据
- 持续广播直到周期结束

## 配置参数

可以通过修改以下常量来调整时序：

```c
static const int LINK_CYCLE_DURATION_MS = 400;  // 总周期时间
static const int LINK_SCAN_DURATION_MS = 150;   // 扫描阶段时间
static const int LINK_BACKOFF_MAX_MS = 20;      // 最大随机back-off时间
static const int LINK_ADV_START_MS = 170;       // 广播开始时间
```

## 注意事项

1. **任务优先级**: Link循环运行在优先级5的FreeRTOS任务中
2. **内存使用**: 任务栈大小为4096字节
3. **线程安全**: 使用互斥锁保护扫描结果
4. **错误处理**: 包含完整的错误检查和日志记录
5. **资源清理**: 停止时自动清理广播和任务资源

## 日志输出

启用调试日志可以看到详细的时序信息：

```
I LINK_CYCLE: Link cycle task started with UUID: ABCD..., Version: 3, Name: MyDevice
D LINK_CYCLE: === Starting new 400ms cycle ===
D LINK_CYCLE: Phase 1: Scanning for 150 ms
D LINK_CYCLE: Phase 2: Random back-off for 15 ms
D LINK_CYCLE: Phase 3: Advertising for 215 ms
D LINK_CYCLE: === Cycle completed ===
```

## 与扫描结果集成

Link循环可以与现有的扫描结果功能集成：

```cpp
// 在link循环运行时获取扫描结果
std::string scan_results = ble_scan_start_and_wait_json_with_filter("ABCD");
// 注意：这会与link循环的扫描阶段冲突，建议在停止link循环后使用
```
