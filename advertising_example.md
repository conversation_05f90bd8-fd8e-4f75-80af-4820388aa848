# BLE广播功能使用示例

## 功能概述

这个BLE广播功能允许您创建一个BLE广播，包含：
- 自定义128位UUID
- 版本信息
- 设备名称
- LINK_PROTOCOL制造商数据

## API函数

### C接口

```c
// 开始广播
int ble_start_advertising(const uint8_t *uuid128, uint8_t version, const char *device_name);

// 停止广播
int ble_stop_advertising(void);
```

### C++接口

```cpp
// 使用十六进制字符串的便利函数
int ble_start_advertising_cpp(const std::string& uuid128_hex, uint8_t version, const std::string& device_name);
```

## 使用示例

### 示例1：使用C接口

```c
// 定义128位UUID (16字节)
uint8_t my_uuid[16] = {
    0x12, 0x34, 0x56, 0x78, 0x12, 0x34, 0x56, 0x78,
    0x9A, 0xBC, 0xDE, 0xF0, 0x12, 0x34, 0x56, 0x78
};

// 开始广播
int result = ble_start_advertising(my_uuid, 2, "MyDevice");
if (result == 0) {
    printf("广播启动成功\n");
} else {
    printf("广播启动失败: %d\n", result);
}

// 停止广播
ble_stop_advertising();
```

### 示例2：使用C++接口

```cpp
// 使用十六进制字符串定义UUID (32个十六进制字符 = 128位)
std::string uuid = "123456781234567889ABCDEF01234567";
uint8_t version = 3;
std::string device_name = "LinkProtocol-Device";

// 开始广播
int result = ble_start_advertising_cpp(uuid, version, device_name);
if (result == 0) {
    ESP_LOGI("APP", "广播启动成功");
} else {
    ESP_LOGE("APP", "广播启动失败: %d", result);
}
```

## 广播数据格式

广播包将包含以下数据：

1. **设备名称**: 完整的本地名称
2. **标志**: 一般可发现 + 不支持BR/EDR
3. **128位服务UUID**: 您提供的自定义UUID
4. **制造商数据**: LINK_PROTOCOL格式
   - 制造商ID: 0xFFFF
   - 协议标识: "LINK" (0x4C494E4B)
   - 版本: 您指定的版本号

## 版本说明

支持的版本：
- 版本1 (0x01): LINK_PROTOCOL_MANUFACTURER_ID_V1
- 版本2 (0x02): LINK_PROTOCOL_MANUFACTURER_ID_V2  
- 版本3 (0x03): LINK_PROTOCOL_MANUFACTURER_ID_V3

## 注意事项

1. UUID必须是128位（16字节或32个十六进制字符）
2. 设备名称不能为空
3. 如果已经在广播，新的广播会先停止旧的
4. 广播参数使用快速间隔以提高可发现性
5. 广播设置为可连接和一般可发现模式

## 错误代码

- `0`: 成功
- `-1`: 参数无效
- 其他: NimBLE错误代码
