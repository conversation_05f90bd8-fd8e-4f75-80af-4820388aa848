{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "D:/Espressif/frameworks/esp-idf-v5.4.2/", "idf.toolsPathWin": "D:\\Espressif", "idf.pythonInstallPath": "D:\\Espressif\\tools\\idf-python\\3.11.2\\python.exe", "idf.flashType": "UART", "idf.portWin": "COM12", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.customExtraVars": {"OPENOCD_SCRIPTS": "D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20240318/openocd-esp32/share/openocd/scripts", "IDF_CCACHE_ENABLE": "1", "ESP_ROM_ELF_DIR": "D:\\Espressif\\tools\\esp-rom-elfs\\20230320/", "IDF_TARGET": "esp32s3"}, "clangd.path": "D:\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=D:\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "--compile-commands-dir=d:\\scanner\\blecent\\build"], "files.associations": {"nimble_port_freertos.h": "c", "ble_svc_gap.h": "c", "ble_hs.h": "c"}}